import os
import shutil
import json
from zipfile import ZipFile
from app.core.constants import (
    MIGRATE_OUTPUT_DIR, S3_URL_EXPIRATION_SECONDS,
    STORAGE_BASE_DIR, MIGRATE_S3_ZIP_PATH, WORKBOOKS_PATH
)
from app.core.config import S3Config, logger
from ..analysis.chart_types import get_chart_types
from pathlib import Path
import uuid 
from app.core.exceptions import BadRequestError,ServerError
from .PowerBI.report.page_report import generate_report_sections
import xml.etree.ElementTree as ET

s3_config = S3Config()
s3_client = s3_config.get_s3_client()
bucket_name = s3_config.bucket_name


def validate_powerbi_structure(base_dir):
    required_components = {
        ".Report": lambda base: any(
            folder.endswith(".Report") and os.path.isdir(os.path.join(base, folder)) 
            and os.path.isfile(os.path.join(base, folder, "report.json"))
            for folder in os.listdir(base) if os.path.isdir(os.path.join(base, folder))
        ),
        ".SemanticModel": lambda base: any(
            folder.endswith(".SemanticModel") and os.path.isdir(os.path.join(base, folder))
            for folder in os.listdir(base) if os.path.isdir(os.path.join(base, folder))
        ),
        ".pbip": lambda base: any(
            f.endswith(".pbip") for f in os.listdir(base)
        )
    }

    missing_components = []

    for component, check in required_components.items():
        if not check(base_dir):
            missing_components.append(component)

    if missing_components:
        return {
            "status": "Invalid",
            "missing_components": missing_components
        }
    else:
        return {
            "status": "Valid",
            "message": "Power BI structure is valid."
        }


async def prepare_powerbi_structure(powerbi_structure, local_dir, logger_id):
    """Extracts and validates the Power BI structure."""
    powerbi_temp_dir = os.path.join(str(local_dir), 'pbi')
    os.makedirs(powerbi_temp_dir, exist_ok=True)

    # Generate a shorter unique ID to avoid path length issues
    unique_id = str(uuid.uuid4().hex[:3])  # Reduced from 5 to 3 characters
    powerbi_zip_filename = f"pbi_{unique_id}.zip"
    powerbi_zip_path = os.path.join(powerbi_temp_dir, powerbi_zip_filename)

    # Check if file exists in S3
    file_exists = await s3_config.check_file_exists(powerbi_structure)
    if not file_exists:
        raise BadRequestError(
            detail=f"PowerBI structure not found in S3: {powerbi_structure}"
        )

    try:
        await s3_config.download_file(powerbi_structure, powerbi_zip_path)
    except Exception as e:
        logger.error(f"{logger_id}-- Failed to download from S3: {str(e)}")
        raise BadRequestError(
            status_code=404,
            detail=f"Failed to download PowerBI structure: {str(e)}"
        )

    # Extract the zip with shorter directory name
    pbi_extracted_dir = os.path.join(powerbi_temp_dir, f"p{unique_id}")  # Shortened from pbi_{unique_id}
    os.makedirs(pbi_extracted_dir, exist_ok=True)
    
    try:
        with ZipFile(powerbi_zip_path, 'r') as powerbi_zip:
            powerbi_zip.extractall(pbi_extracted_dir)
    except Exception as e:
        raise ServerError(
            detail=f"Failed to extract PowerBI structure: {str(e)}"
        )

    powerbi_folder_path = [f.path for f in os.scandir(pbi_extracted_dir) if f.is_dir()]
    powerbi_folder_path = powerbi_folder_path[0] if len(powerbi_folder_path) == 1 else pbi_extracted_dir 

    # Validate and return the folder path
    validation_result = validate_powerbi_structure(powerbi_folder_path)
    if validation_result.get('status') == "Invalid":
        raise ValueError(f"Invalid Power BI structure: {validation_result.get('missing_components')}")
    logger.info(f"{logger_id}-- PowerBI zip file extracted and validated")
    return powerbi_folder_path


def get_report_filepath(folder_path):
    powerbi_folder_list = os.listdir(folder_path)
    powerbi_report_folder = [path for path in powerbi_folder_list if '.Report' in path]
    report_filepath = os.path.join(folder_path,powerbi_report_folder[0],'report.json')
    return report_filepath


def modify_pbi_reportfile(source_path, dest_path, overall_json):
    """Copies and modifies the Power BI structure."""
    # Ensure the base destination directory exists
    dest_path = os.path.abspath(dest_path)
    source_path = os.path.abspath(source_path)
    os.makedirs(dest_path, exist_ok=True)

    for root, dirs, files in os.walk(source_path):
        for filename in files:
            src_path = os.path.join(root, filename)
            relative_path = os.path.relpath(src_path, source_path)
            dest_file_path = os.path.join(dest_path, relative_path)

            # Ensure destination directory exists - use absolute path
            dest_dir = os.path.dirname(dest_file_path)
            if dest_dir and dest_dir != dest_path:
                try:
                    os.makedirs(dest_dir, exist_ok=True)
                    logger.info(f"Created directory: {dest_dir}")
                except Exception as e:
                    logger.error(f"Failed to create directory {dest_dir}: {e}")
                    raise

            try:
                shutil.copy2(src_path, dest_file_path)
                logger.info(f"Successfully copied file: {src_path} -> {dest_file_path}")
            except Exception as e:
                logger.error(f"Failed to copy file {src_path} to {dest_file_path}: {e}")
                raise

    source_report_path = get_report_filepath(source_path)
    with open(source_report_path, 'r') as report_file:
        report_template = json.load(report_file)
    report_template['sections'] = overall_json
    # report_template['pods'] = pods_list

    dest_report_path = get_report_filepath(dest_path)
    with open(dest_report_path, 'w') as dest_file:
        json.dump(report_template, dest_file, indent=4)


def process_single_twb_file(twb_file, local_dir, powerbi_folder_path, s3_input_path, custom_zip_name=None):
    twb_filename = os.path.basename(twb_file)
    logger.info(f" ---Started {twb_filename} migration-------")
    tree = ET.parse(twb_file)
    root = tree.getroot()
    chart_types = get_chart_types(twb_file)
    overall_json = generate_report_sections(root, chart_types)
    # Use simple directory structure to avoid Windows path length issues
    # Format: storage/org_name/migrated_outputs/report_name
    base_name = os.path.splitext(twb_filename)[0]
    org_name = str(local_dir).split(os.sep)[-2] if len(str(local_dir).split(os.sep)) > 1 else "default"
    migrated_outputs_dir = os.path.join("storage", org_name, "migrated_outputs")
    os.makedirs(migrated_outputs_dir, exist_ok=True)
    powerbi_output_dir = os.path.join(migrated_outputs_dir, base_name)
    os.makedirs(powerbi_output_dir, exist_ok=True)
    modify_pbi_reportfile(source_path = powerbi_folder_path, dest_path = powerbi_output_dir, overall_json = overall_json)
    return upload_and_generate_download_link(
        powerbi_output_dir, s3_input_path, custom_zip_name=custom_zip_name
    )


async def process_files_for_migration(
        twb_files,
        powerbi_structure,
        process_id,
        s3_input_path,
        custom_zip_name=None
):
    base_dir = STORAGE_BASE_DIR
    # Use shorter path to avoid Windows path length limitations
    # Extract just the organization and report ID for a shorter path
    s3_path_obj = Path(s3_input_path)
    path_parts = s3_path_obj.parts
    if len(path_parts) >= 3:
        # Use format: storage/org/reportid_short/migrate_outputs
        org_name = path_parts[1] if len(path_parts) > 1 else "default"
        report_id = path_parts[2] if len(path_parts) > 2 else "default"
        short_report_id = report_id[:8]  # Use first 8 characters
        local_dir = Path(f"{base_dir}/{org_name}/{short_report_id}/{MIGRATE_OUTPUT_DIR}")
    else:
        # Fallback to very short path
        local_dir = Path(f"{base_dir}/temp/{process_id[:8]}/{MIGRATE_OUTPUT_DIR}")
    local_dir.mkdir(parents=True, exist_ok=True)
    powerbi_structure = await prepare_powerbi_structure(powerbi_structure, local_dir, process_id)
    download_links = []
    for twb_file in twb_files:
        download_link = await process_single_twb_file(
            twb_file, local_dir, powerbi_structure, s3_input_path, custom_zip_name=custom_zip_name
        )
        download_links.append(download_link)
    return download_links


async def upload_and_generate_download_link(powerbi_output_dir, s3_input_path, custom_zip_name=None) -> dict:
    parent_s3_folder = os.path.dirname(s3_input_path)
    zip_path = shutil.make_archive(str(powerbi_output_dir), 'zip', str(powerbi_output_dir))
    zip_file = Path(zip_path)
    if custom_zip_name:
        custom_zip_path = os.path.join(os.path.dirname(zip_path), custom_zip_name)
        if Path(custom_zip_path).exists(): os.remove(custom_zip_path)
        os.rename(zip_path, custom_zip_path)
        zip_s3_key = f"{parent_s3_folder}/{MIGRATE_OUTPUT_DIR}/{custom_zip_name}"
        file_name_to_return = custom_zip_name
        upload_path = custom_zip_path
    else:
        zip_s3_key = f"{parent_s3_folder}/{MIGRATE_OUTPUT_DIR}/{zip_file.name}"
        file_name_to_return = zip_file.name
        upload_path = str(zip_path)
    await s3_config.upload_to_s3(file_path=upload_path, object_name=zip_s3_key)
    return {
        'file': file_name_to_return,
        'download_url': await s3_config.generate_presigned_url(
            object_key=zip_s3_key,
        )
    }
